# TiDB SSL连接问题修复指南

## 🔍 问题分析

根据错误日志，问题出现在SSL握手阶段：

```
AttributeError: 'dict' object has no attribute 'wrap_bio'
```

这个错误表明aiomysql期望接收一个SSL上下文对象，而不是字典。

## 🔧 修复方案

### 1. 修复SSL配置代码

**问题代码（修复前）**:
```python
ssl_context = {}
ssl_context["check_hostname"] = False
ssl_context["verify_mode"] = ssl.CERT_NONE
connection_params["ssl"] = ssl_context  # 错误：传递字典
```

**修复代码（修复后）**:
```python
import ssl
ssl_context = ssl.create_default_context()  # 创建SSL上下文对象
ssl_context.check_hostname = False
ssl_context.verify_mode = ssl.CERT_NONE
connection_params["ssl"] = ssl_context  # 正确：传递SSL上下文对象
```

### 2. 配置文件调整

**修改 `config/config.yml`**:
```yaml
database:
  # TLS/SSL 配置
  ssl_disabled: false
  ssl_verify_cert: true
  ssl_verify_identity: true
  ssl_ca: null  # 改为null，使用系统默认CA
  ssl_cert: null
  ssl_key: null
```

## 🧪 测试连接

运行测试脚本验证修复效果：

```bash
python test_db_connection.py
```

预期输出：
```
==================================================
TiDB 连接测试
==================================================
连接参数:
  主机: gateway01.ap-southeast-1.prod.aws.tidbcloud.com
  端口: 4000
  用户: 2bmga6guXNxtZeu.root
  SSL: 启用
  SSL验证: 启用

正在连接...
✓ 数据库连接成功
✓ 数据库版本: 8.0.11-TiDB-v7.x.x
✓ TiDB 版本: Release Version: v7.x.x
✓ 数据库 'danmu' 已存在

✓ 连接测试完成
```

## 🔄 重启应用

修复完成后，重启应用：

```bash
# 在宝塔面板中重启Python项目
# 或使用命令行
python main.py
```

## 🚨 故障排除

### 如果仍然出现SSL错误

1. **尝试禁用证书验证**:
   ```yaml
   database:
     ssl_verify_cert: false
   ```

2. **检查TiDB Cloud连接信息**:
   - 确认主机地址正确
   - 确认端口号（通常是4000）
   - 确认用户名和密码

3. **网络连接测试**:
   ```bash
   telnet gateway01.ap-southeast-1.prod.aws.tidbcloud.com 4000
   ```

### 常见错误及解决方案

| 错误信息 | 可能原因 | 解决方案 |
|----------|----------|----------|
| `SSL handshake failed` | SSL配置错误 | 检查SSL上下文创建 |
| `Access denied` | 认证失败 | 检查用户名密码 |
| `Connection refused` | 网络问题 | 检查主机地址和端口 |
| `Certificate verify failed` | 证书验证失败 | 设置ssl_verify_cert: false |

## 📝 修复总结

1. **修复了SSL配置**: 使用正确的SSL上下文对象
2. **调整了配置文件**: 移除了不存在的CA证书路径
3. **提供了测试工具**: 便于验证连接状态
4. **添加了故障排除**: 常见问题的解决方案

修复后，应用应该能够正常连接到TiDB数据库并启动成功。
