#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
御坂网络弹幕服务 - 宝塔面板部署启动脚本

这个文件是为宝塔面板Python项目部署而创建的启动入口点。
它会导入并启动FastAPI应用程序。

使用方法：
1. 在宝塔面板中创建Python项目
2. 将此文件设置为启动文件
3. 确保已安装requirements.txt中的所有依赖
4. 配置config/config.yml中的数据库连接信息
"""

import sys
import os
import uvicorn
from pathlib import Path

# 确保项目根目录在Python路径中
project_root = Path(__file__).parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

# 导入应用程序
from src.main import app

def main():
    """主函数 - 启动FastAPI应用"""
    # 从配置中读取服务器设置
    from src.config import settings
    
    print("="*60)
    print("御坂网络弹幕服务正在启动...")
    print(f"服务器地址: {settings.server.host}:{settings.server.port}")
    print(f"数据库地址: {settings.database.host}:{settings.database.port}")
    print(f"数据库名称: {settings.database.name}")
    print(f"SSL状态: {'已启用' if not settings.database.ssl_disabled else '已禁用'}")
    print("="*60)
    
    # 启动服务器
    uvicorn.run(
        app,
        host=settings.server.host,
        port=settings.server.port,
        log_level=settings.log.level.lower(),
        access_log=True,
        # 在生产环境中建议设置为False
        reload=False
    )

if __name__ == "__main__":
    main()
