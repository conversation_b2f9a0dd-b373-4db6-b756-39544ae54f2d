# 宝塔面板部署指南

本文档详细说明如何在宝塔面板中部署御坂网络弹幕服务，并配置 TiDB 数据库连接。

## 前置要求

### 1. 服务器要求
- Linux 服务器（CentOS 7+、Ubuntu 18.04+、Debian 9+）
- 至少 1GB 内存
- 至少 10GB 可用磁盘空间
- 已安装宝塔面板 7.0+

### 2. 宝塔面板环境
- Python 3.8+ 环境
- 已安装 Python 项目管理器

### 3. TiDB 数据库
- TiDB Cloud 免费账户
- 已创建 Serverless 集群

## 详细部署步骤

### 步骤 1: 准备 TiDB 数据库

1. **注册 TiDB Cloud**
   - 访问 https://tidbcloud.com/
   - 注册免费账户
   - 验证邮箱

2. **创建集群**
   - 选择 "Serverless" 免费版本
   - 选择离您最近的区域
   - 设置集群名称
   - 创建 root 用户密码

3. **获取连接信息**
   - 在集群详情页点击 "Connect"
   - 选择 "General" 连接方式
   - 记录以下信息：
     - Host（主机地址）
     - Port（端口，通常是 4000）
     - Username（用户名）
     - Password（密码）

### 步骤 2: 下载和配置项目

1. **下载项目代码**
   ```bash
   # 在服务器上执行
   cd /www/wwwroot/
   git clone https://github.com/l429609201/misaka_danmu_server.git
   cd misaka_danmu_server
   ```

2. **配置数据库连接**
   编辑 `config/config.yml` 文件：
   ```yaml
   # 服务器配置
   server:
     host: "0.0.0.0"
     port: 7768

   # 数据库配置 - 替换为您的 TiDB 信息
   database:
     host: "gateway01.ap-northeast-1.prod.aws.tidbcloud.com"  # 您的TiDB主机
     port: 4000
     user: "root"  # 您的TiDB用户名
     password: "your_tidb_password"  # 您的TiDB密码
     name: "danmaku_db"
     ssl_disabled: false  # TiDB 必须启用 SSL
     ssl_verify_cert: true
     ssl_verify_identity: true

   # JWT 配置 - 请修改为复杂密钥
   jwt:
     secret_key: "your_very_secure_jwt_secret_key_here"
     algorithm: "HS256"
     access_token_expire_minutes: 1440

   # 初始管理员配置
   admin:
     initial_user: "admin"
     initial_password: null  # 留空自动生成

   # 日志配置
   log:
     level: "INFO"
   ```

### 步骤 3: 宝塔面板配置

1. **安装 Python 依赖**
   - 登录宝塔面板
   - 进入 "软件商店" -> "Python项目管理器"
   - 如果没有安装，先安装 Python 项目管理器

2. **创建 Python 项目**
   - 点击 "添加Python项目"
   - 项目名称：`misaka_danmu_server`
   - 项目路径：`/www/wwwroot/misaka_danmu_server`
   - Python版本：选择 3.8 或更高版本
   - 启动方式：`python`
   - 启动文件：`main.py`
   - 端口：`7768`

3. **安装项目依赖**
   - 在项目详情页，点击 "模块"
   - 点击 "安装模块"
   - 选择 "从文件安装"
   - 选择项目目录下的 `requirements.txt`
   - 等待安装完成

4. **启动项目**
   - 在项目详情页，点击 "启动"
   - 查看日志确认启动成功
   - 记录初始管理员密码（在日志中显示）

### 步骤 4: 防火墙和域名配置

1. **开放端口**
   - 在宝塔面板 "安全" 页面
   - 添加端口规则：`7768`
   - 确保服务器防火墙也开放此端口

2. **配置域名（可选）**
   - 在宝塔面板 "网站" 页面
   - 添加站点，绑定域名
   - 配置反向代理到 `127.0.0.1:7768`

### 步骤 5: 访问和初始化

1. **访问 Web 界面**
   - 浏览器访问：`http://您的服务器IP:7768`
   - 或使用配置的域名

2. **初始登录**
   - 用户名：`admin`
   - 密码：查看宝塔面板项目日志中的随机密码

3. **修改密码**
   - 登录后立即在 "设置" -> "账户安全" 中修改密码

4. **配置 API 密钥**
   - 在 "设置" 页面配置各种 API 密钥（TMDB、豆瓣等）

## 常见问题解决

### 数据库连接失败

**错误信息**: `无法创建数据库连接池`

**解决方案**:
1. 检查 TiDB 连接信息是否正确
2. 确认 `ssl_disabled: false`
3. 检查服务器网络连接
4. 验证 TiDB 用户权限

### 端口被占用

**错误信息**: `Address already in use`

**解决方案**:
1. 修改 `config/config.yml` 中的端口号
2. 或停止占用端口的其他服务

### 依赖安装失败

**解决方案**:
1. 确保 Python 版本 >= 3.8
2. 更新 pip：`pip install --upgrade pip`
3. 手动安装失败的包

### 权限问题

**解决方案**:
1. 确保项目目录权限正确：`chown -R www:www /www/wwwroot/misaka_danmu_server`
2. 检查 config 目录写权限

## 维护和更新

### 查看日志
- 宝塔面板项目详情页的 "日志" 选项卡
- 或直接查看：`/www/wwwroot/misaka_danmu_server/logs/`

### 重启服务
- 在宝塔面板项目详情页点击 "重启"

### 更新代码
```bash
cd /www/wwwroot/misaka_danmu_server
git pull origin main
# 在宝塔面板中重启项目
```

### 备份数据
- TiDB 数据会自动备份到云端
- 建议定期备份 `config/config.yml` 配置文件

## 性能优化建议

1. **启用 Gzip 压缩**（如使用反向代理）
2. **配置 CDN**（如使用域名访问）
3. **监控资源使用**（通过宝塔面板监控）
4. **定期清理日志**（避免磁盘空间不足）

## 安全建议

1. **修改默认端口**（避免使用 7768）
2. **配置 HTTPS**（如使用域名）
3. **定期更新密码**
4. **限制访问 IP**（如果可能）
5. **启用防火墙**
6. **定期更新系统和依赖**
