# 御坂网络弹幕服务

[![GitHub](https://img.shields.io/badge/-GitHub-181717?logo=github)](https://github.com/l429609201/misaka_danmu_server)
![GitHub License](https://img.shields.io/github/license/l429609201/misaka_danmu_server)
[![GitHub release (latest SemVer)](https://img.shields.io/github/v/release/l429609201/misaka_danmu_server?color=blue&label=download&sort=semver)](https://github.com/l429609201/misaka_danmu_server/releases/latest)
[![telegram](https://img.shields.io/static/v1?label=telegram&message=misaka_danmu_server&color=blue)](https://t.me/misaka_danmu_server)

一个功能强大的自托管弹幕（Danmaku）聚合与管理服务，兼容 [dandanplay](https://api.dandanplay.net/swagger/index.html) API 规范。

本项目旨在通过刮削主流视频网站的弹幕，为您自己的媒体库提供一个统一、私有的弹幕API。它自带一个现代化的Web界面，方便您管理弹幕库、搜索源、API令牌和系统设置。

## ✨ 核心功能

- **多源刮削**: 自动从 Bilibili、腾讯视频、爱奇艺、优酷等多个来源获取弹幕。
- **智能匹配**: 通过文件名或元数据（TMDB, TVDB等）智能匹配您的影视文件，提供准确的弹幕。
- **Web管理界面**: 提供一个直观的Web UI，用于：
  - 搜索和手动导入弹幕。
  - 管理已收录的媒体库、数据源和分集。
  - 创建和管理供第三方客户端（如 yamby, hills, 小幻影视）使用的API令牌。
  - 配置搜索源的优先级和启用状态。
  - 查看后台任务进度和系统日志。
- **元数据整合**: 支持与 TMDB, TVDB, Bangumi, Douban, IMDb 集成，丰富您的媒体信息。
- **自动化**: 支持通过 Webhook 接收来自 Sonarr, Radarr, Emby 等服务的通知，实现全自动化的弹幕导入。
- **灵活部署**: 提供 Docker 镜像和 Docker Compose 文件，方便快速部署。

## 其他

### 推广须知

- 请不要在 ***B站*** 或中国大陆社交平台发布视频或文章宣传本项目

## 🚀 快速开始

本项目支持两种部署方式：宝塔面板部署（推荐）和 Docker 部署。

### 方式一：宝塔面板部署（推荐）

#### 前置要求

1. **TiDB 数据库**: 推荐使用 [TiDB Cloud](https://tidbcloud.com/) 免费版本
2. **宝塔面板**: 已安装宝塔面板的Linux服务器
3. **Python 3.8+**: 宝塔面板已安装Python环境

#### 步骤 1: 准备 TiDB 数据库

1. 注册并登录 [TiDB Cloud](https://tidbcloud.com/)
2. 创建一个免费的 Serverless 集群
3. 获取连接信息：
   - 主机地址 (Host)
   - 端口 (Port, 通常是 4000)
   - 用户名 (Username)
   - 密码 (Password)

#### 步骤 2: 部署应用

1. **下载项目代码**:
   ```bash
   git clone https://github.com/l429609201/misaka_danmu_server.git
   cd misaka_danmu_server
   ```

2. **配置数据库连接**:
   编辑 `config/config.yml` 文件，修改数据库配置：
   ```yaml
   database:
     host: "your-tidb-host.clusters.tidb-cloud.com"  # 您的TiDB主机地址
     port: 4000  # TiDB端口
     user: "your_username"  # 您的TiDB用户名
     password: "your_password"  # 您的TiDB密码
     name: "danmaku_db"  # 数据库名称
     ssl_disabled: false  # TiDB需要启用SSL
   ```

3. **安装依赖**:
   ```bash
   pip install -r requirements.txt
   ```

4. **在宝塔面板中创建Python项目**:
   - 项目类型：Python项目
   - 启动文件：`main.py`
   - 项目目录：上传的项目文件夹路径
   - 端口：7768（或其他可用端口）

5. **启动项目**: 在宝塔面板中启动Python项目

#### 步骤 3: 访问和配置

- **访问Web UI**: 打开浏览器，访问 `http://<您的服务器IP>:7768`
- **初始登录**:
  - 用户名: `admin`
  - 密码: 首次启动时会在日志中生成随机密码，请查看宝塔面板的项目日志
- **开始使用**: 登录后，请先在 "设置" -> "账户安全" 中修改您的密码

### 方式二：Docker 部署

如果您更喜欢使用 Docker，可以参考以下配置：

```yaml
version: '3.8'
services:
  danmu-app:
    image: l429609201/misaka_danmu_server:latest
    container_name: misaka-danmu-server
    restart: unless-stopped
    environment:
      - DANMUAPI_DATABASE__HOST=your-tidb-host.clusters.tidb-cloud.com
      - DANMUAPI_DATABASE__PORT=4000
      - DANMUAPI_DATABASE__USER=your_username
      - DANMUAPI_DATABASE__PASSWORD=your_password
      - DANMUAPI_DATABASE__NAME=danmaku_db
      - DANMUAPI_DATABASE__SSL_DISABLED=false
      - DANMUAPI_ADMIN__INITIAL_USER=admin
    ports:
      - "7768:7768"
    volumes:
      - ./config:/app/config
```

## 客户端配置

### 1. 获取弹幕 Token

- 在 Web UI 的 "弹幕Token" 页面，点击 "添加Token" 来创建一个新的访问令牌。
- 创建后，您会得到一串随机字符，这就是您的弹幕 Token。
- 可通过配置自定义域名之后直接点击复制，会帮你拼接好相关的链接

### 2. 配置弹幕接口

在您的播放器（如 Yamby, Hills, 小幻影视等）的自定义弹幕接口设置中，填入以下格式的地址：

`http://<服务器IP>:<端口>/api/<你的Token>`

- `<服务器IP>`: 部署本服务的主机 IP 地址。
- `<端口>`: 部署本服务时设置的端口（默认为 `7768`）。
- `<你的Token>`: 您在上一步中创建的 Token 字符串。

**示例:**

假设您的服务部署在 `*************`，端口为 `7768`，创建的 Token 是 `Q2KHYcveM0SaRKvxomQm`。


- **对于 Yamby （版本要大于********） / Hills （版本要大于1.4.0）:**

  在自定义弹幕接口中填写：
  `http://*************:7768/api/Q2KHYcveM0SaRKvxomQm`
- **对于 小幻影视:**
  小幻影视可能需要一个包含 `/api/v2` 的路径，您可以填写：
  `http://*************:7768/api/Q2KHYcveM0SaRKvxomQm/api/v2`

> **兼容性说明**: 本服务已对路由进行特殊处理，无论您使用 `.../api/<Token>` 还是 `.../api/<Token>/api/v2` 格式，服务都能正确响应，以最大程度兼容不同客户端。

## Webhook 配置

本服务支持通过 Webhook 接收来自 Emby 等媒体服务器的通知，实现新媒体入库后的弹幕自动搜索和导入。

### 1. 获取 Webhook URL

1. 在 Web UI 的 "设置" -> "Webhook" 页面，您会看到一个为您生成的唯一的 **API Key**。
2. 根据您要集成的服务，复制对应的 Webhook URL。URL 的通用格式为：
   `http://<服务器IP>:<端口>/api/webhook/{服务名}?api_key=<你的API_Key>`

   - `<服务器IP>`: 部署本服务的主机 IP 地址。
   - `<端口>`: 部署本服务时设置的端口（默认为 `7768`）。
   - `{服务名}`: webhook界面中下方已加载的服务名称，例如 `emby`。
   - `<你的API_Key>`: 您在 Webhook 设置页面获取的密钥。
3. 现在已经增加拼接URL后的复制按钮

### 2. 配置媒体服务器

- **对于Emby**

  1. 登录您的 Emby 服务器管理后台。
  2. 导航到 **通知** (Notifications)。
  3. 点击 **添加通知** (Add Notification)，选择 **Webhook** 类型。
  4. 在 **Webhook URL** 字段中，填入您的 Emby Webhook URL，例如：
     ```
     http://*************:7768/api/webhook/emby?api_key=your_webhook_api_key_here
     ```
  5. **关键步骤**: 在 **事件** (Events) 部分，请务必**只勾选**以下事件：
     - **项目已添加 (Item Added)**: 这是新媒体入库的事件，其对应的事件名为 `新媒体添加`。
  6. 确保 **发送内容类型** (Content type) 设置为 `application/json`。
  7. 保存设置。
- **对于Jellyfin**

  1. 登录您的 Jellyfin 服务器管理后台。
  2. 导航到 **我的插件**，找到 **Webhook** 插件，如果没有找到，请先安装插件，并重启服务器。
  3. 点击 **Webhook** 插件，进入配置页面。
  4. 在 **Server Url** 中输入jellyfin 访问地址（可选）。
  5. 点击 **Add Generic Destination**。
  6. 输入 **Webhook Name**
  7. 在 **Webhook URL** 字段中，填入您的 Jellyfin Webhook URL，例如：
     ```
     http://*************:7768/api/webhook/jellyfin?api_key=your_webhook_api_key_here
     ```
  8. **关键步骤**: 在 **Notification Type** 部分，请务必**只勾选**以下事件：
     - **Item Added**: 这是新媒体入库的事件，其对应的事件名为 `新媒体添加`。
  9. **关键步骤**: 一定要勾选 **Send All Properties (ignores template)** 选项。
  10. 保存设置。

现在，当有新的电影或剧集添加到您的 Emby/Jellyfin 媒体库时，本服务将自动收到通知，并创建一个后台任务来为其搜索和导入弹幕。

## TiDB 数据库配置详解

### 为什么选择 TiDB？

- **免费额度**: TiDB Cloud 提供免费的 Serverless 集群，足够个人使用
- **高可用性**: 云端托管，无需维护本地数据库
- **兼容性**: 完全兼容 MySQL 协议
- **安全性**: 强制启用 TLS 加密连接

### TiDB 连接配置说明

在 `config/config.yml` 中的数据库配置项说明：

```yaml
database:
  host: "gateway01.ap-northeast-1.prod.aws.tidbcloud.com"  # TiDB 集群地址
  port: 4000  # TiDB 默认端口
  user: "your_username"  # 数据库用户名
  password: "your_password"  # 数据库密码
  name: "danmaku_db"  # 数据库名称（会自动创建）

  # TLS 配置（TiDB 必需）
  ssl_disabled: false  # 必须为 false，TiDB 要求 TLS
  ssl_verify_cert: true  # 验证服务器证书
  ssl_verify_identity: true  # 验证服务器身份
  ssl_ca: null  # CA证书路径（TiDB Cloud 通常不需要）
  ssl_cert: null  # 客户端证书（如需双向认证）
  ssl_key: null  # 客户端私钥（如需双向认证）
```

### 获取 TiDB 连接信息

1. 登录 [TiDB Cloud 控制台](https://tidbcloud.com/)
2. 选择您的集群
3. 点击 "Connect" 按钮
4. 选择 "General" 连接方式
5. 复制连接参数到配置文件

### 故障排除

**连接失败常见原因：**

1. **SSL 配置错误**: 确保 `ssl_disabled: false`
2. **网络问题**: 检查服务器是否能访问外网
3. **认证失败**: 验证用户名和密码是否正确
4. **数据库不存在**: 应用会自动创建数据库，确保用户有创建权限

**查看连接日志：**
- 宝塔面板：查看Python项目的运行日志
- 命令行：`python main.py` 查看启动日志

### 贡献者

<a href="https://github.com/l429609201/misaka_danmu_server/graphs/contributors">
  <img src="https://contrib.rocks/image?repo=l429609201/misaka_danmu_server" alt="contributors" />
</a>
