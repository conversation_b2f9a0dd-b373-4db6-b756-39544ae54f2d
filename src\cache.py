#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库查询缓存模块

基于TiDB最佳实践实现的应用层缓存，减少数据库查询压力。
支持TTL过期、LRU淘汰策略和缓存统计。
"""

import asyncio
import hashlib
import json
import time
from typing import Any, Dict, List, Optional, Callable, Awaitable
from functools import wraps
import logging

logger = logging.getLogger(__name__)

class QueryCache:
    """应用层查询缓存"""
    
    def __init__(self, max_size: int = 1000, default_ttl: int = 300):
        """
        初始化缓存
        
        Args:
            max_size: 最大缓存条目数
            default_ttl: 默认TTL（秒）
        """
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.access_times: Dict[str, float] = {}
        self.stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0
        }
        
    def _generate_key(self, func_name: str, args: tuple, kwargs: dict) -> str:
        """生成缓存键"""
        # 创建包含函数名、参数的唯一键
        key_data = {
            'func': func_name,
            'args': args,
            'kwargs': {k: v for k, v in kwargs.items() if k not in ['pool']}  # 排除连接池
        }
        key_str = json.dumps(key_data, sort_keys=True, default=str)
        return hashlib.md5(key_str.encode()).hexdigest()
    
    def _is_expired(self, cache_entry: Dict[str, Any]) -> bool:
        """检查缓存是否过期"""
        return time.time() > cache_entry['expires_at']
    
    def _evict_lru(self):
        """LRU淘汰策略"""
        if len(self.cache) >= self.max_size:
            # 找到最久未访问的键
            lru_key = min(self.access_times.keys(), key=lambda k: self.access_times[k])
            del self.cache[lru_key]
            del self.access_times[lru_key]
            self.stats['evictions'] += 1
            logger.debug(f"缓存淘汰: {lru_key}")
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        if key not in self.cache:
            self.stats['misses'] += 1
            return None
        
        cache_entry = self.cache[key]
        
        # 检查是否过期
        if self._is_expired(cache_entry):
            del self.cache[key]
            del self.access_times[key]
            self.stats['misses'] += 1
            return None
        
        # 更新访问时间
        self.access_times[key] = time.time()
        self.stats['hits'] += 1
        return cache_entry['data']
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """设置缓存值"""
        if ttl is None:
            ttl = self.default_ttl
        
        # 检查是否需要淘汰
        self._evict_lru()
        
        expires_at = time.time() + ttl
        self.cache[key] = {
            'data': value,
            'expires_at': expires_at,
            'created_at': time.time()
        }
        self.access_times[key] = time.time()
        
    def clear(self) -> None:
        """清空缓存"""
        self.cache.clear()
        self.access_times.clear()
        logger.info("缓存已清空")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        total_requests = self.stats['hits'] + self.stats['misses']
        hit_rate = self.stats['hits'] / total_requests if total_requests > 0 else 0
        
        return {
            'size': len(self.cache),
            'max_size': self.max_size,
            'hits': self.stats['hits'],
            'misses': self.stats['misses'],
            'hit_rate': hit_rate,
            'evictions': self.stats['evictions']
        }

# 全局缓存实例
query_cache = QueryCache(max_size=1000, default_ttl=300)  # 5分钟默认TTL

def cached_query(ttl: int = 300, cache_key_func: Optional[Callable] = None):
    """
    查询缓存装饰器
    
    Args:
        ttl: 缓存TTL（秒）
        cache_key_func: 自定义缓存键生成函数
    """
    def decorator(func: Callable[..., Awaitable[Any]]):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 生成缓存键
            if cache_key_func:
                cache_key = cache_key_func(*args, **kwargs)
            else:
                cache_key = query_cache._generate_key(func.__name__, args, kwargs)
            
            # 尝试从缓存获取
            cached_result = query_cache.get(cache_key)
            if cached_result is not None:
                logger.debug(f"缓存命中: {func.__name__}")
                return cached_result
            
            # 执行原函数
            logger.debug(f"缓存未命中，执行查询: {func.__name__}")
            result = await func(*args, **kwargs)
            
            # 存入缓存
            query_cache.set(cache_key, result, ttl)
            
            return result
        return wrapper
    return decorator

def cache_key_for_search(keyword: str, limit: int = 50) -> str:
    """搜索查询的缓存键生成器"""
    return f"search:{hashlib.md5(keyword.encode()).hexdigest()}:{limit}"

def cache_key_for_anime_details(anime_id: int) -> str:
    """番剧详情的缓存键生成器"""
    return f"anime_details:{anime_id}"

def cache_key_for_library(limit: int, offset: int) -> str:
    """媒体库的缓存键生成器"""
    return f"library:{limit}:{offset}"

# 缓存管理函数
async def warm_up_cache():
    """缓存预热"""
    logger.info("开始缓存预热...")
    # 这里可以预加载一些常用数据
    # 例如：热门番剧、最新番剧等
    pass

async def cleanup_expired_cache():
    """清理过期缓存（定期任务）"""
    expired_keys = []
    current_time = time.time()
    
    for key, cache_entry in query_cache.cache.items():
        if current_time > cache_entry['expires_at']:
            expired_keys.append(key)
    
    for key in expired_keys:
        del query_cache.cache[key]
        del query_cache.access_times[key]
    
    if expired_keys:
        logger.info(f"清理了 {len(expired_keys)} 个过期缓存条目")

# 缓存统计和监控
def log_cache_stats():
    """记录缓存统计信息"""
    stats = query_cache.get_stats()
    logger.info(f"缓存统计: {stats}")

# 针对特定查询的缓存策略
class CacheStrategy:
    """缓存策略配置"""
    
    # 不同类型查询的TTL配置
    TTL_CONFIG = {
        'anime_search': 600,      # 搜索结果缓存10分钟
        'anime_details': 1800,    # 番剧详情缓存30分钟
        'library_list': 300,      # 媒体库列表缓存5分钟
        'episode_list': 900,      # 分集列表缓存15分钟
        'comment_count': 60,      # 弹幕数量缓存1分钟
    }
    
    @classmethod
    def get_ttl(cls, query_type: str) -> int:
        """获取指定查询类型的TTL"""
        return cls.TTL_CONFIG.get(query_type, 300)  # 默认5分钟

# 缓存失效策略
class CacheInvalidation:
    """缓存失效管理"""
    
    @staticmethod
    def invalidate_anime_cache(anime_id: int):
        """使番剧相关缓存失效"""
        patterns = [
            f"anime_details:{anime_id}",
            f"library:",  # 媒体库列表可能包含此番剧
        ]
        
        keys_to_remove = []
        for key in query_cache.cache.keys():
            for pattern in patterns:
                if pattern in key:
                    keys_to_remove.append(key)
        
        for key in keys_to_remove:
            if key in query_cache.cache:
                del query_cache.cache[key]
            if key in query_cache.access_times:
                del query_cache.access_times[key]
        
        logger.info(f"使 {len(keys_to_remove)} 个相关缓存失效")
    
    @staticmethod
    def invalidate_search_cache():
        """使搜索缓存失效"""
        keys_to_remove = [key for key in query_cache.cache.keys() if key.startswith("search:")]
        
        for key in keys_to_remove:
            if key in query_cache.cache:
                del query_cache.cache[key]
            if key in query_cache.access_times:
                del query_cache.access_times[key]
        
        logger.info(f"使 {len(keys_to_remove)} 个搜索缓存失效")
