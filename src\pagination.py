#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分页和查询限制优化模块

基于TiDB最佳实践实现的分页策略，提高大数据量查询性能。
"""

from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field
import math

class PaginationParams(BaseModel):
    """分页参数模型"""
    page: int = Field(1, ge=1, description="页码，从1开始")
    page_size: int = Field(20, ge=1, le=100, description="每页大小，最大100")
    
    @property
    def offset(self) -> int:
        """计算偏移量"""
        return (self.page - 1) * self.page_size
    
    @property
    def limit(self) -> int:
        """获取限制数量"""
        return self.page_size

class PaginationResult(BaseModel):
    """分页结果模型"""
    items: List[Any] = Field([], description="数据项")
    total: int = Field(0, description="总数量")
    page: int = Field(1, description="当前页码")
    page_size: int = Field(20, description="每页大小")
    total_pages: int = Field(0, description="总页数")
    has_next: bool = Field(False, description="是否有下一页")
    has_prev: bool = Field(False, description="是否有上一页")
    
    @classmethod
    def create(cls, items: List[Any], total: int, pagination: PaginationParams) -> "PaginationResult":
        """创建分页结果"""
        total_pages = math.ceil(total / pagination.page_size) if total > 0 else 0
        
        return cls(
            items=items,
            total=total,
            page=pagination.page,
            page_size=pagination.page_size,
            total_pages=total_pages,
            has_next=pagination.page < total_pages,
            has_prev=pagination.page > 1
        )

class QueryLimits:
    """查询限制配置"""
    
    # 不同查询类型的默认限制
    DEFAULT_LIMITS = {
        'search': 50,           # 搜索结果
        'library': 100,         # 媒体库列表
        'episodes': 200,        # 分集列表
        'comments': 1000,       # 弹幕列表
        'admin_logs': 500,      # 管理日志
    }
    
    # 最大限制（防止过大查询）
    MAX_LIMITS = {
        'search': 100,
        'library': 500,
        'episodes': 1000,
        'comments': 5000,
        'admin_logs': 1000,
    }
    
    @classmethod
    def get_safe_limit(cls, query_type: str, requested_limit: Optional[int] = None) -> int:
        """获取安全的查询限制"""
        default_limit = cls.DEFAULT_LIMITS.get(query_type, 50)
        max_limit = cls.MAX_LIMITS.get(query_type, 100)
        
        if requested_limit is None:
            return default_limit
        
        return min(requested_limit, max_limit)

def optimize_query_with_pagination(
    base_query: str, 
    pagination: PaginationParams,
    order_by: Optional[str] = None
) -> tuple[str, tuple]:
    """
    为查询添加分页优化
    
    Args:
        base_query: 基础查询语句
        pagination: 分页参数
        order_by: 排序字段
    
    Returns:
        优化后的查询语句和参数
    """
    # 添加排序（如果没有的话）
    if order_by and "ORDER BY" not in base_query.upper():
        base_query += f" ORDER BY {order_by}"
    
    # 添加分页
    paginated_query = f"{base_query} LIMIT %s OFFSET %s"
    params = (pagination.limit, pagination.offset)
    
    return paginated_query, params

def create_count_query(base_query: str) -> str:
    """
    从基础查询创建计数查询
    
    Args:
        base_query: 基础查询语句
    
    Returns:
        计数查询语句
    """
    # 移除ORDER BY子句（计数不需要排序）
    query_upper = base_query.upper()
    order_by_pos = query_upper.find("ORDER BY")
    if order_by_pos != -1:
        base_query = base_query[:order_by_pos]
    
    # 移除LIMIT子句
    limit_pos = query_upper.find("LIMIT")
    if limit_pos != -1:
        base_query = base_query[:limit_pos]
    
    # 创建计数查询
    # 如果有GROUP BY，需要特殊处理
    if "GROUP BY" in query_upper:
        count_query = f"SELECT COUNT(*) as total FROM ({base_query}) as subquery"
    else:
        # 简单的计数查询
        select_pos = query_upper.find("SELECT")
        from_pos = query_upper.find("FROM")
        
        if select_pos != -1 and from_pos != -1:
            count_query = f"SELECT COUNT(*) as total {base_query[from_pos:]}"
        else:
            # 回退方案
            count_query = f"SELECT COUNT(*) as total FROM ({base_query}) as subquery"
    
    return count_query

class CursorPagination:
    """游标分页（适用于大数据集）"""
    
    @staticmethod
    def create_cursor_query(
        base_query: str,
        cursor_field: str,
        cursor_value: Optional[Any] = None,
        limit: int = 50,
        direction: str = "next"
    ) -> tuple[str, tuple]:
        """
        创建游标分页查询
        
        Args:
            base_query: 基础查询
            cursor_field: 游标字段（通常是id或时间戳）
            cursor_value: 游标值
            limit: 限制数量
            direction: 方向（next/prev）
        
        Returns:
            查询语句和参数
        """
        params = []
        
        if cursor_value is not None:
            if direction == "next":
                cursor_condition = f"{cursor_field} > %s"
            else:
                cursor_condition = f"{cursor_field} < %s"
            
            # 添加游标条件
            if "WHERE" in base_query.upper():
                base_query += f" AND {cursor_condition}"
            else:
                base_query += f" WHERE {cursor_condition}"
            
            params.append(cursor_value)
        
        # 添加排序和限制
        if direction == "next":
            order_direction = "ASC"
        else:
            order_direction = "DESC"
        
        if "ORDER BY" not in base_query.upper():
            base_query += f" ORDER BY {cursor_field} {order_direction}"
        
        base_query += " LIMIT %s"
        params.append(limit)
        
        return base_query, tuple(params)

# 性能优化建议
class QueryOptimizationTips:
    """查询优化建议"""
    
    TIPS = {
        'use_indexes': "确保查询条件字段有适当的索引",
        'limit_results': "始终使用LIMIT限制结果集大小",
        'avoid_select_star': "避免使用SELECT *，只选择需要的字段",
        'use_pagination': "对大数据集使用分页查询",
        'optimize_joins': "优化JOIN操作，确保连接字段有索引",
        'use_explain': "使用EXPLAIN分析查询执行计划",
        'cache_results': "对频繁查询的结果进行缓存",
        'batch_operations': "批量操作替代单条操作"
    }
    
    @classmethod
    def get_tips_for_query_type(cls, query_type: str) -> List[str]:
        """获取特定查询类型的优化建议"""
        common_tips = ['use_indexes', 'limit_results', 'use_pagination']
        
        type_specific_tips = {
            'search': ['avoid_select_star', 'cache_results'],
            'join_heavy': ['optimize_joins', 'use_explain'],
            'bulk_insert': ['batch_operations'],
            'analytics': ['use_explain', 'cache_results']
        }
        
        tips = common_tips + type_specific_tips.get(query_type, [])
        return [cls.TIPS[tip] for tip in tips if tip in cls.TIPS]

# 查询性能监控
class QueryPerformanceMonitor:
    """查询性能监控"""
    
    def __init__(self):
        self.slow_queries = []
        self.query_stats = {}
    
    def record_query(self, query_type: str, execution_time: float, result_count: int):
        """记录查询性能"""
        if query_type not in self.query_stats:
            self.query_stats[query_type] = {
                'count': 0,
                'total_time': 0,
                'avg_time': 0,
                'max_time': 0,
                'total_results': 0
            }
        
        stats = self.query_stats[query_type]
        stats['count'] += 1
        stats['total_time'] += execution_time
        stats['avg_time'] = stats['total_time'] / stats['count']
        stats['max_time'] = max(stats['max_time'], execution_time)
        stats['total_results'] += result_count
        
        # 记录慢查询（超过1秒）
        if execution_time > 1.0:
            self.slow_queries.append({
                'type': query_type,
                'time': execution_time,
                'result_count': result_count,
                'timestamp': time.time()
            })
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        return {
            'query_stats': self.query_stats,
            'slow_queries': self.slow_queries[-10:],  # 最近10个慢查询
            'total_queries': sum(stats['count'] for stats in self.query_stats.values())
        }

# 全局性能监控实例
performance_monitor = QueryPerformanceMonitor()
