<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover" />
  <title>御坂弹幕 · 移动版</title>
  <link rel="icon" href="/static/logo.png" type="image/png" />
  <link rel="stylesheet" href="/static/css/mobile.css" />
</head>
<body>
  <div id="app">
    <!-- 登录视图 -->
    <section id="auth-screen">
      <div class="auth-card">
        <div class="brand">
          <img src="/static/logo.png" alt="logo" />
          <h1>御坂网络弹幕</h1>
        </div>
        <form id="login-form">
          <input id="login-username" type="text" placeholder="用户名" autocomplete="username" required />
          <input id="login-password" type="password" placeholder="密码" autocomplete="current-password" required />
          <button type="submit" class="primary">登录</button>
          <p id="auth-error" class="message error"></p>
        </form>
      </div>
    </section>

    <!-- 主视图 -->
    <section id="main-screen" class="hidden">
      <header class="topbar">
        <div class="brand-mini">
          <img src="/static/logo.png" alt="logo" />
          <span>御坂弹幕 · 移动</span>
        </div>
        <div class="user">
          <span id="current-user-name">...</span>
          <button id="logout-btn" class="text-btn">退出</button>
        </div>
      </header>

      <main class="content" id="main-content">
        <section id="search-card" class="card search-sticky">
          <form id="search-form" class="search-bar">
            <input id="search-input" type="text" inputmode="search" placeholder="输入番剧名称，支持 SxxExx" required />
            <button type="submit" class="primary">搜索</button>
          </form>
          <div id="loader" class="loader hidden"></div>
          <div id="search-progress" class="progress hidden"><div class="bar"></div><span id="search-progress-label" class="label">0%</span></div>
        </section>

        <section id="results-card" class="card">
          <h2>搜索结果</h2>
          <div id="results-skeleton" class="skeleton hidden"></div>
          <div id="results-empty" class="empty hidden">
            <div class="empty-illustration"></div>
            <p class="empty-title">还没有结果</p>
            <p class="empty-sub">试试输入完整片名，或用 SxxExx 精确到集数</p>
            <div class="empty-actions">
              <button id="btn-demo-search" class="row-action">示例搜索</button>
            </div>
          </div>
          <ul id="results-list" class="list"></ul>
        </section>

        <section id="tasks-card" class="card hidden">
          <h2>任务</h2>
          <div id="tasks-progress" class="progress hidden"><div class="bar"></div><span id="tasks-progress-label" class="label">0%</span></div>
          <ul id="tasks-list" class="list"></ul>
        </section>

        <section id="library-card" class="card hidden">
          <h2>弹幕库</h2>
          <ul id="library-list" class="list"></ul>
        </section>

        <section id="tokens-card" class="card hidden">
          <h2>Token 管理</h2>
          <div class="section">
            <h3 class="small">自定义域名</h3>
            <div class="grid-2">
              <input id="token-custom-domain-input" placeholder="例如 https://danmu.example.com" />
              <button id="token-save-domain-btn" class="row-action">保存域名</button>
            </div>
            <div id="token-domain-save-msg" class="message small"></div>
          </div>
          <div class="section">
            <h3 class="small">全局 User-Agent 过滤</h3>
            <div class="grid-2">
              <select id="token-ua-filter-mode">
                <option value="off">关闭</option>
                <option value="blacklist">黑名单</option>
                <option value="whitelist">白名单</option>
              </select>
              <div class="grid-2-no-gap">
                <button id="token-save-ua-mode-btn" class="row-action">保存模式</button>
                <button id="token-manage-ua-list-btn" class="row-action">管理名单</button>
              </div>
            </div>
            <div id="token-ua-mode-save-msg" class="message small"></div>
          </div>
          <div class="section">
            <h3 class="small">新增 Token</h3>
            <div class="grid-3">
              <input id="token-new-name" placeholder="名称" />
              <select id="token-validity">
                <option value="permanent" selected>永久</option>
                <option value="1d">1 天</option>
                <option value="7d">7 天</option>
                <option value="30d">30 天</option>
                <option value="180d">6 个月</option>
                <option value="365d">1 年</option>
              </select>
              <button id="token-add-btn" class="row-action">添加</button>
            </div>
          </div>
          <ul id="token-list" class="list"></ul>
        </section>

        <section id="tokens-ua-card" class="card hidden">
          <div class="grid-2">
            <button id="token-ua-back-btn" class="row-action">返回</button>
            <div></div>
          </div>
          <h2>管理 UA 名单</h2>
          <div class="grid-2">
            <input id="token-ua-new" placeholder="添加 UA 关键字" />
            <button id="token-ua-add-btn" class="row-action">添加</button>
          </div>
          <ul id="token-ua-list" class="list"></ul>
        </section>

        <section id="tokens-log-card" class="card hidden">
          <div class="grid-2">
            <button id="token-log-back-btn" class="row-action">返回</button>
            <div></div>
          </div>
          <h2 id="token-log-title">Token 访问日志</h2>
          <ul id="token-log-list" class="list"></ul>
        </section>

        <section id="settings-card" class="card hidden">
          <h2>设置</h2>
          <div class="subnav">
            <button id="mset-tab-account" class="subnav-btn">账户</button>
            <button id="mset-tab-webhook" class="subnav-btn">Webhook</button>
            <button id="mset-tab-bangumi" class="subnav-btn">Bangumi</button>
            <button id="mset-tab-tmdb" class="subnav-btn">TMDB</button>
            <button id="mset-tab-douban" class="subnav-btn">豆瓣</button>
            <button id="mset-tab-tvdb" class="subnav-btn">TVDB</button>
          </div>

          <div id="mset-account" class="settings-view hidden">
            <div class="section">
              <h3 class="small">修改密码</h3>
              <div class="grid-1">
                <input id="mset-old-password" type="password" placeholder="当前密码" />
                <input id="mset-new-password" type="password" placeholder="新密码（至少8位）" />
                <input id="mset-confirm-password" type="password" placeholder="确认新密码" />
                <button id="mset-save-password-btn" class="row-action">确认修改</button>
                <div id="mset-password-msg" class="message small"></div>
              </div>
            </div>
          </div>

          <div id="mset-webhook" class="settings-view hidden">
            <div class="section">
              <h3 class="small">API Key</h3>
              <div class="grid-2">
                <input id="mset-webhook-api-key" readonly />
                <button id="mset-regenerate-webhook-key" class="row-action">重新生成</button>
              </div>
            </div>
            <div class="section">
              <h3 class="small">自定义域名</h3>
              <div class="grid-2">
                <input id="mset-webhook-domain" placeholder="https://danmu.example.com" />
                <button id="mset-save-webhook-domain" class="row-action">保存域名</button>
              </div>
              <div id="mset-webhook-domain-msg" class="message small"></div>
            </div>
            <div class="section">
              <h3 class="small">URL 生成</h3>
              <div class="grid-1">
                <select id="mset-webhook-service"></select>
                <div class="grid-2">
                  <input id="mset-webhook-url" readonly placeholder="选择服务后生成" />
                  <button id="mset-copy-webhook-url" class="row-action">复制</button>
                </div>
              </div>
            </div>
          </div>

          <div id="mset-bangumi" class="settings-view hidden">
            <div class="section">
              <div class="grid-1">
                <input id="mset-bgm-client-id" placeholder="App ID" />
                <input id="mset-bgm-client-secret" type="password" placeholder="App Secret" />
                <button id="mset-save-bgm" class="row-action">保存</button>
              </div>
            </div>
            <div class="section">
              <div class="grid-2">
                <button id="mset-bgm-login" class="row-action">登录</button>
                <button id="mset-bgm-logout" class="row-action hidden">注销</button>
              </div>
              <div id="mset-bgm-state" class="small"></div>
            </div>
          </div>

          <div id="mset-tmdb" class="settings-view hidden">
            <div class="grid-1">
              <input id="mset-tmdb-key" type="password" placeholder="API Key (v3)" />
              <input id="mset-tmdb-api-base" placeholder="API 域名 e.g. https://api.themoviedb.org" />
              <input id="mset-tmdb-img-base" placeholder="图片域名 e.g. https://image.tmdb.org/t/p/w500" />
              <button id="mset-save-tmdb" class="row-action">保存</button>
              <div id="mset-tmdb-msg" class="message small"></div>
            </div>
          </div>

          <div id="mset-douban" class="settings-view hidden">
            <div class="grid-1">
              <textarea id="mset-douban-cookie" rows="4" placeholder="豆瓣 Cookie（提高成功率，选填）"></textarea>
              <button id="mset-save-douban" class="row-action">保存</button>
              <div id="mset-douban-msg" class="message small"></div>
            </div>
          </div>

          <div id="mset-tvdb" class="settings-view hidden">
            <div class="grid-1">
              <input id="mset-tvdb-key" type="password" placeholder="TVDB API Key" />
              <button id="mset-save-tvdb" class="row-action">保存</button>
              <div id="mset-tvdb-msg" class="message small"></div>
            </div>
          </div>
        </section>
      </main>

      <nav class="bottom-nav">
        <button id="tab-search" class="nav-btn active">搜索</button>
        <button id="tab-library" class="nav-btn">库</button>
        <button id="tab-tasks" class="nav-btn">任务</button>
        <button id="tab-tokens" class="nav-btn">Token</button>
        <button id="tab-settings" class="nav-btn">设置</button>
        <span id="nav-indicator" class="nav-indicator"></span>
      </nav>
    </section>
  </div>

  <script type="module" src="/static/js/mobile.js"></script>
</body>
</html>