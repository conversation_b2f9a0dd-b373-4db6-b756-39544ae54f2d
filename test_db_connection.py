#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的TiDB连接测试脚本
"""

import asyncio
import sys
import ssl
from pathlib import Path

# 确保项目根目录在Python路径中
project_root = Path(__file__).parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

import aiomysql
from src.config import settings

async def test_connection():
    """测试数据库连接"""
    print("="*50)
    print("TiDB 连接测试")
    print("="*50)
    
    try:
        # 构建连接参数
        connection_params = {
            "host": settings.database.host,
            "port": settings.database.port,
            "user": settings.database.user,
            "password": settings.database.password,
            "charset": "utf8mb4"
        }
        
        print(f"连接参数:")
        print(f"  主机: {settings.database.host}")
        print(f"  端口: {settings.database.port}")
        print(f"  用户: {settings.database.user}")
        print(f"  SSL: {'启用' if not settings.database.ssl_disabled else '禁用'}")
        
        # 配置SSL
        if not settings.database.ssl_disabled:
            ssl_context = ssl.create_default_context()
            
            if not settings.database.ssl_verify_cert:
                ssl_context.check_hostname = False
                ssl_context.verify_mode = ssl.CERT_NONE
                print("  SSL验证: 禁用")
            else:
                ssl_context.verify_mode = ssl.CERT_REQUIRED
                print("  SSL验证: 启用")
                
                if settings.database.ssl_ca:
                    ssl_context.load_verify_locations(settings.database.ssl_ca)
                    print(f"  CA证书: {settings.database.ssl_ca}")
            
            connection_params["ssl"] = ssl_context
        
        print("\n正在连接...")
        
        # 测试连接
        conn = await aiomysql.connect(**connection_params)
        print("✓ 数据库连接成功")
        
        # 测试基本查询
        async with conn.cursor() as cursor:
            await cursor.execute("SELECT VERSION()")
            version = await cursor.fetchone()
            print(f"✓ 数据库版本: {version[0]}")
            
            # 检查是否是TiDB
            try:
                await cursor.execute("SELECT @@tidb_version")
                tidb_version = await cursor.fetchone()
                if tidb_version and tidb_version[0]:
                    print(f"✓ TiDB 版本: {tidb_version[0]}")
                else:
                    print("⚠ 警告: 这不是TiDB数据库")
            except Exception as e:
                print(f"⚠ 无法获取TiDB版本: {e}")
            
            # 测试数据库创建
            db_name = settings.database.name
            try:
                await cursor.execute("SHOW DATABASES LIKE %s", (db_name,))
                exists = await cursor.fetchone()
                
                if exists:
                    print(f"✓ 数据库 '{db_name}' 已存在")
                else:
                    print(f"数据库 '{db_name}' 不存在，尝试创建...")
                    await cursor.execute(f"CREATE DATABASE `{db_name}` CHARACTER SET utf8mb4 COLLATE utf8mb4_bin")
                    print(f"✓ 数据库 '{db_name}' 创建成功")
            except Exception as e:
                print(f"✗ 数据库操作失败: {e}")
        
        conn.close()
        print("\n✓ 连接测试完成")
        return True
        
    except Exception as e:
        print(f"\n✗ 连接失败: {e}")
        print(f"错误类型: {type(e).__name__}")
        
        # 提供一些常见问题的解决建议
        error_str = str(e).lower()
        if "ssl" in error_str:
            print("\n建议:")
            print("- 检查SSL配置是否正确")
            print("- 尝试禁用SSL验证: ssl_verify_cert: false")
        elif "access denied" in error_str:
            print("\n建议:")
            print("- 检查用户名和密码是否正确")
            print("- 确认TiDB用户权限")
        elif "connection refused" in error_str or "timeout" in error_str:
            print("\n建议:")
            print("- 检查网络连接")
            print("- 确认主机地址和端口")
            print("- 检查防火墙设置")
        
        return False

if __name__ == "__main__":
    success = asyncio.run(test_connection())
    sys.exit(0 if success else 1)
