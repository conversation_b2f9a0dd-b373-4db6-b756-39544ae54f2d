#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库性能监控模块

基于TiDB最佳实践实现的数据库性能监控和指标收集。
"""

import time
import asyncio
import logging
from typing import Dict, Any, List, Optional, Callable
from functools import wraps
from datetime import datetime, timedelta
import aiomysql
from contextlib import asynccontextmanager

logger = logging.getLogger(__name__)

class DatabaseMetrics:
    """数据库性能指标收集器"""
    
    def __init__(self):
        self.query_metrics = {}
        self.connection_metrics = {
            'total_connections': 0,
            'active_connections': 0,
            'failed_connections': 0,
            'connection_errors': []
        }
        self.slow_queries = []
        self.error_queries = []
        
    def record_query(self, 
                    query_type: str, 
                    execution_time: float, 
                    result_count: int = 0,
                    error: Optional[str] = None):
        """记录查询指标"""
        if query_type not in self.query_metrics:
            self.query_metrics[query_type] = {
                'count': 0,
                'total_time': 0.0,
                'avg_time': 0.0,
                'min_time': float('inf'),
                'max_time': 0.0,
                'error_count': 0,
                'total_results': 0,
                'last_executed': None
            }
        
        metrics = self.query_metrics[query_type]
        metrics['count'] += 1
        metrics['last_executed'] = datetime.now()
        
        if error:
            metrics['error_count'] += 1
            self.error_queries.append({
                'type': query_type,
                'error': error,
                'timestamp': datetime.now(),
                'execution_time': execution_time
            })
        else:
            metrics['total_time'] += execution_time
            metrics['avg_time'] = metrics['total_time'] / (metrics['count'] - metrics['error_count'])
            metrics['min_time'] = min(metrics['min_time'], execution_time)
            metrics['max_time'] = max(metrics['max_time'], execution_time)
            metrics['total_results'] += result_count
            
            # 记录慢查询（超过1秒）
            if execution_time > 1.0:
                self.slow_queries.append({
                    'type': query_type,
                    'execution_time': execution_time,
                    'result_count': result_count,
                    'timestamp': datetime.now()
                })
                logger.warning(f"慢查询检测: {query_type} 耗时 {execution_time:.2f}s")
    
    def record_connection_event(self, event_type: str, error: Optional[str] = None):
        """记录连接事件"""
        if event_type == 'connect':
            self.connection_metrics['total_connections'] += 1
            self.connection_metrics['active_connections'] += 1
        elif event_type == 'disconnect':
            self.connection_metrics['active_connections'] = max(0, 
                self.connection_metrics['active_connections'] - 1)
        elif event_type == 'error':
            self.connection_metrics['failed_connections'] += 1
            if error:
                self.connection_metrics['connection_errors'].append({
                    'error': error,
                    'timestamp': datetime.now()
                })
    
    def get_summary(self) -> Dict[str, Any]:
        """获取性能指标摘要"""
        total_queries = sum(m['count'] for m in self.query_metrics.values())
        total_errors = sum(m['error_count'] for m in self.query_metrics.values())
        
        return {
            'query_summary': {
                'total_queries': total_queries,
                'total_errors': total_errors,
                'error_rate': total_errors / total_queries if total_queries > 0 else 0,
                'slow_queries_count': len(self.slow_queries),
                'query_types': len(self.query_metrics)
            },
            'connection_summary': self.connection_metrics,
            'top_slow_queries': sorted(self.slow_queries, 
                                     key=lambda x: x['execution_time'], 
                                     reverse=True)[:10],
            'recent_errors': self.error_queries[-10:] if self.error_queries else []
        }
    
    def get_detailed_metrics(self) -> Dict[str, Any]:
        """获取详细指标"""
        return {
            'query_metrics': self.query_metrics,
            'connection_metrics': self.connection_metrics,
            'slow_queries': self.slow_queries[-50:],  # 最近50个慢查询
            'error_queries': self.error_queries[-50:]  # 最近50个错误查询
        }
    
    def reset_metrics(self):
        """重置指标"""
        self.query_metrics.clear()
        self.slow_queries.clear()
        self.error_queries.clear()
        self.connection_metrics = {
            'total_connections': 0,
            'active_connections': 0,
            'failed_connections': 0,
            'connection_errors': []
        }
        logger.info("数据库指标已重置")

# 全局指标收集器
db_metrics = DatabaseMetrics()

def monitor_query(query_type: str):
    """查询监控装饰器"""
    def decorator(func: Callable):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            error = None
            result_count = 0
            
            try:
                result = await func(*args, **kwargs)
                if isinstance(result, list):
                    result_count = len(result)
                elif isinstance(result, dict) and 'items' in result:
                    result_count = len(result['items'])
                return result
            except Exception as e:
                error = str(e)
                logger.error(f"查询错误 {query_type}: {error}")
                raise
            finally:
                execution_time = time.time() - start_time
                db_metrics.record_query(query_type, execution_time, result_count, error)
        
        return wrapper
    return decorator

@asynccontextmanager
async def monitored_connection(pool: aiomysql.Pool):
    """监控的数据库连接上下文管理器"""
    conn = None
    try:
        db_metrics.record_connection_event('connect')
        conn = await pool.acquire()
        yield conn
    except Exception as e:
        db_metrics.record_connection_event('error', str(e))
        raise
    finally:
        if conn:
            pool.release(conn)
            db_metrics.record_connection_event('disconnect')

class TiDBSpecificMonitor:
    """TiDB特定的监控指标"""
    
    @staticmethod
    async def get_tidb_status(pool: aiomysql.Pool) -> Dict[str, Any]:
        """获取TiDB状态信息"""
        try:
            async with monitored_connection(pool) as conn:
                async with conn.cursor(aiomysql.DictCursor) as cursor:
                    # 检查TiDB版本
                    await cursor.execute("SELECT @@tidb_version as version")
                    version_info = await cursor.fetchone()
                    
                    # 检查连接数
                    await cursor.execute("SHOW STATUS LIKE 'Threads_connected'")
                    connections = await cursor.fetchone()
                    
                    # 检查查询统计
                    await cursor.execute("SHOW STATUS LIKE 'Questions'")
                    questions = await cursor.fetchone()
                    
                    # 检查慢查询
                    await cursor.execute("SHOW STATUS LIKE 'Slow_queries'")
                    slow_queries = await cursor.fetchone()
                    
                    return {
                        'tidb_version': version_info['version'] if version_info else 'Unknown',
                        'connections': int(connections['Value']) if connections else 0,
                        'total_questions': int(questions['Value']) if questions else 0,
                        'slow_queries': int(slow_queries['Value']) if slow_queries else 0,
                        'timestamp': datetime.now()
                    }
        except Exception as e:
            logger.error(f"获取TiDB状态失败: {e}")
            return {'error': str(e), 'timestamp': datetime.now()}
    
    @staticmethod
    async def analyze_query_performance(pool: aiomysql.Pool, query: str) -> Dict[str, Any]:
        """分析查询性能"""
        try:
            async with monitored_connection(pool) as conn:
                async with conn.cursor(aiomysql.DictCursor) as cursor:
                    # 使用EXPLAIN ANALYZE分析查询
                    explain_query = f"EXPLAIN ANALYZE {query}"
                    await cursor.execute(explain_query)
                    explain_result = await cursor.fetchall()
                    
                    return {
                        'query': query,
                        'explain_result': explain_result,
                        'timestamp': datetime.now()
                    }
        except Exception as e:
            logger.error(f"查询性能分析失败: {e}")
            return {'error': str(e), 'query': query, 'timestamp': datetime.now()}

class PerformanceAlerts:
    """性能告警系统"""
    
    def __init__(self):
        self.alert_thresholds = {
            'slow_query_time': 2.0,      # 慢查询阈值（秒）
            'error_rate': 0.05,          # 错误率阈值（5%）
            'connection_failures': 10,    # 连接失败阈值
            'query_count_spike': 1000     # 查询数量激增阈值
        }
        self.alerts = []
    
    def check_alerts(self, metrics: Dict[str, Any]):
        """检查告警条件"""
        current_time = datetime.now()
        
        # 检查慢查询
        slow_queries = metrics.get('slow_queries', [])
        recent_slow = [q for q in slow_queries 
                      if (current_time - q['timestamp']).seconds < 300]  # 5分钟内
        
        if len(recent_slow) > 5:
            self._create_alert('slow_queries', 
                             f"5分钟内检测到{len(recent_slow)}个慢查询")
        
        # 检查错误率
        query_summary = metrics.get('query_summary', {})
        error_rate = query_summary.get('error_rate', 0)
        
        if error_rate > self.alert_thresholds['error_rate']:
            self._create_alert('high_error_rate', 
                             f"错误率过高: {error_rate:.2%}")
        
        # 检查连接失败
        connection_metrics = metrics.get('connection_metrics', {})
        failed_connections = connection_metrics.get('failed_connections', 0)
        
        if failed_connections > self.alert_thresholds['connection_failures']:
            self._create_alert('connection_failures', 
                             f"连接失败次数过多: {failed_connections}")
    
    def _create_alert(self, alert_type: str, message: str):
        """创建告警"""
        alert = {
            'type': alert_type,
            'message': message,
            'timestamp': datetime.now(),
            'severity': self._get_severity(alert_type)
        }
        
        self.alerts.append(alert)
        logger.warning(f"性能告警: {alert_type} - {message}")
        
        # 保持最近100个告警
        if len(self.alerts) > 100:
            self.alerts = self.alerts[-100:]
    
    def _get_severity(self, alert_type: str) -> str:
        """获取告警严重程度"""
        severity_map = {
            'slow_queries': 'warning',
            'high_error_rate': 'critical',
            'connection_failures': 'error',
            'query_spike': 'warning'
        }
        return severity_map.get(alert_type, 'info')
    
    def get_active_alerts(self) -> List[Dict[str, Any]]:
        """获取活跃告警"""
        # 返回最近1小时的告警
        one_hour_ago = datetime.now() - timedelta(hours=1)
        return [alert for alert in self.alerts 
                if alert['timestamp'] > one_hour_ago]

# 全局告警系统
performance_alerts = PerformanceAlerts()

# 定期监控任务
async def periodic_monitoring_task(pool: aiomysql.Pool, interval: int = 300):
    """定期监控任务（每5分钟）"""
    while True:
        try:
            # 获取TiDB状态
            tidb_status = await TiDBSpecificMonitor.get_tidb_status(pool)
            logger.info(f"TiDB状态: {tidb_status}")
            
            # 获取性能指标
            metrics = db_metrics.get_summary()
            
            # 检查告警
            performance_alerts.check_alerts(metrics)
            
            # 记录关键指标
            logger.info(f"数据库性能摘要: {metrics['query_summary']}")
            
        except Exception as e:
            logger.error(f"定期监控任务失败: {e}")
        
        await asyncio.sleep(interval)

# 性能报告生成
def generate_performance_report() -> Dict[str, Any]:
    """生成性能报告"""
    metrics = db_metrics.get_detailed_metrics()
    alerts = performance_alerts.get_active_alerts()
    
    return {
        'report_time': datetime.now(),
        'metrics': metrics,
        'alerts': alerts,
        'recommendations': _generate_recommendations(metrics)
    }

def _generate_recommendations(metrics: Dict[str, Any]) -> List[str]:
    """生成优化建议"""
    recommendations = []
    
    # 分析慢查询
    slow_queries = metrics.get('slow_queries', [])
    if len(slow_queries) > 10:
        recommendations.append("检测到大量慢查询，建议优化查询语句或添加索引")
    
    # 分析错误率
    query_metrics = metrics.get('query_metrics', {})
    for query_type, stats in query_metrics.items():
        error_rate = stats['error_count'] / stats['count'] if stats['count'] > 0 else 0
        if error_rate > 0.1:
            recommendations.append(f"{query_type}查询错误率过高({error_rate:.1%})，需要检查")
    
    # 分析连接问题
    connection_metrics = metrics.get('connection_metrics', {})
    if connection_metrics.get('failed_connections', 0) > 5:
        recommendations.append("连接失败次数较多，检查网络和数据库配置")
    
    return recommendations
