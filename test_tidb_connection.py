#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TiDB 连接测试脚本

此脚本用于测试修改后的代码是否能正常连接到TiDB数据库，
并验证基本的数据库操作功能。

使用方法：
python test_tidb_connection.py
"""

import asyncio
import sys
import os
from pathlib import Path

# 确保项目根目录在Python路径中
project_root = Path(__file__).parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

import aiomysql
from src.config import settings

async def test_basic_connection():
    """测试基本数据库连接"""
    print("="*60)
    print("测试 1: 基本数据库连接")
    print("="*60)
    
    try:
        # 构建连接参数
        connection_params = {
            "host": settings.database.host,
            "port": settings.database.port,
            "user": settings.database.user,
            "password": settings.database.password,
            "charset": "utf8mb4"
        }
        
        # 配置 TLS/SSL 参数
        if not settings.database.ssl_disabled:
            ssl_context = {}
            
            if not settings.database.ssl_verify_cert:
                ssl_context["check_hostname"] = False
                ssl_context["verify_mode"] = __import__("ssl").CERT_NONE
            else:
                ssl_context["verify_mode"] = __import__("ssl").CERT_REQUIRED
                
                if settings.database.ssl_ca:
                    ssl_context["ca_certs"] = settings.database.ssl_ca
                
                if settings.database.ssl_cert and settings.database.ssl_key:
                    ssl_context["certfile"] = settings.database.ssl_cert
                    ssl_context["keyfile"] = settings.database.ssl_key
            
            if ssl_context:
                connection_params["ssl"] = ssl_context
                print(f"✓ SSL/TLS 已启用")
        
        print(f"连接参数:")
        print(f"  主机: {settings.database.host}")
        print(f"  端口: {settings.database.port}")
        print(f"  用户: {settings.database.user}")
        print(f"  SSL: {'启用' if not settings.database.ssl_disabled else '禁用'}")
        
        # 测试连接
        conn = await aiomysql.connect(**connection_params)
        print("✓ 数据库连接成功")
        
        # 测试基本查询
        async with conn.cursor() as cursor:
            await cursor.execute("SELECT VERSION()")
            version = await cursor.fetchone()
            print(f"✓ 数据库版本: {version[0]}")
            
            # 检查是否是TiDB
            await cursor.execute("SELECT @@tidb_version")
            tidb_version = await cursor.fetchone()
            if tidb_version and tidb_version[0]:
                print(f"✓ TiDB 版本: {tidb_version[0]}")
            else:
                print("⚠ 警告: 这不是TiDB数据库")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"✗ 连接失败: {e}")
        return False

async def test_database_creation():
    """测试数据库创建"""
    print("\n" + "="*60)
    print("测试 2: 数据库创建")
    print("="*60)
    
    try:
        # 连接到服务器（不指定数据库）
        connection_params = {
            "host": settings.database.host,
            "port": settings.database.port,
            "user": settings.database.user,
            "password": settings.database.password,
            "charset": "utf8mb4"
        }
        
        if not settings.database.ssl_disabled:
            ssl_context = {}
            if not settings.database.ssl_verify_cert:
                ssl_context["check_hostname"] = False
                ssl_context["verify_mode"] = __import__("ssl").CERT_NONE
            else:
                ssl_context["verify_mode"] = __import__("ssl").CERT_REQUIRED
                if settings.database.ssl_ca:
                    ssl_context["ca_certs"] = settings.database.ssl_ca
                if settings.database.ssl_cert and settings.database.ssl_key:
                    ssl_context["certfile"] = settings.database.ssl_cert
                    ssl_context["keyfile"] = settings.database.ssl_key
            if ssl_context:
                connection_params["ssl"] = ssl_context
        
        conn = await aiomysql.connect(**connection_params)
        
        async with conn.cursor() as cursor:
            # 检查数据库是否存在
            db_name = settings.database.name
            await cursor.execute("SHOW DATABASES LIKE %s", (db_name,))
            exists = await cursor.fetchone()
            
            if exists:
                print(f"✓ 数据库 '{db_name}' 已存在")
            else:
                print(f"数据库 '{db_name}' 不存在，尝试创建...")
                await cursor.execute(f"CREATE DATABASE `{db_name}` CHARACTER SET utf8mb4 COLLATE utf8mb4_bin")
                print(f"✓ 数据库 '{db_name}' 创建成功")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"✗ 数据库创建失败: {e}")
        return False

async def test_table_creation():
    """测试表创建"""
    print("\n" + "="*60)
    print("测试 3: 表创建（测试表）")
    print("="*60)
    
    try:
        # 连接到指定数据库
        connection_params = {
            "host": settings.database.host,
            "port": settings.database.port,
            "user": settings.database.user,
            "password": settings.database.password,
            "db": settings.database.name,
            "charset": "utf8mb4"
        }
        
        if not settings.database.ssl_disabled:
            ssl_context = {}
            if not settings.database.ssl_verify_cert:
                ssl_context["check_hostname"] = False
                ssl_context["verify_mode"] = __import__("ssl").CERT_NONE
            else:
                ssl_context["verify_mode"] = __import__("ssl").CERT_REQUIRED
                if settings.database.ssl_ca:
                    ssl_context["ca_certs"] = settings.database.ssl_ca
                if settings.database.ssl_cert and settings.database.ssl_key:
                    ssl_context["certfile"] = settings.database.ssl_cert
                    ssl_context["keyfile"] = settings.database.ssl_key
            if ssl_context:
                connection_params["ssl"] = ssl_context
        
        conn = await aiomysql.connect(**connection_params)
        
        async with conn.cursor() as cursor:
            # 创建测试表
            test_table_sql = """
            CREATE TABLE IF NOT EXISTS test_tidb_compatibility (
                id BIGINT NOT NULL AUTO_INCREMENT,
                title VARCHAR(255) NOT NULL,
                content TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                INDEX idx_title (title)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            """
            
            await cursor.execute(test_table_sql)
            print("✓ 测试表创建成功")
            
            # 测试插入数据
            await cursor.execute(
                "INSERT INTO test_tidb_compatibility (title, content) VALUES (%s, %s)",
                ("测试标题", "测试内容")
            )
            print("✓ 数据插入成功")
            
            # 测试查询数据
            await cursor.execute("SELECT * FROM test_tidb_compatibility WHERE title LIKE %s", ("%测试%",))
            result = await cursor.fetchone()
            if result:
                print(f"✓ 数据查询成功: ID={result[0]}, 标题={result[1]}")
            
            # 清理测试表
            await cursor.execute("DROP TABLE test_tidb_compatibility")
            print("✓ 测试表清理完成")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"✗ 表操作失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("TiDB 兼容性测试开始")
    print(f"配置文件: config/config.yml")
    
    # 执行所有测试
    tests = [
        test_basic_connection,
        test_database_creation,
        test_table_creation
    ]
    
    results = []
    for test in tests:
        result = await test()
        results.append(result)
    
    # 总结
    print("\n" + "="*60)
    print("测试总结")
    print("="*60)
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"✓ 所有测试通过 ({passed}/{total})")
        print("✓ TiDB 连接和基本功能正常")
        return 0
    else:
        print(f"✗ 部分测试失败 ({passed}/{total})")
        print("✗ 请检查配置和网络连接")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
