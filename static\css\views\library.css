#library-table .poster-cell img {
    width: 50px;
    height: 70px;
    object-fit: cover;
    border-radius: 4px;
    background-color: #eee;
}

.anime-detail-header-main {
    display: flex;
    gap: 20px;
    align-items: center;
    flex-grow: 1;
}

.anime-detail-header-main img {
    width: 100px;
    height: 140px;
    object-fit: cover;
    border-radius: 4px;
    flex-shrink: 0;
}

.episode-list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}
.episode-list-header .header-actions {
    display: flex;
    gap: 10px;
}


.episode-list-header h3 {
    margin: 0;
}

#danmaku-list-view pre {
    flex-grow: 1;
    overflow-y: auto;
    background-color: #f8f9fa;
    border: 1px solid var(--border-color);
    padding: 10px;
    white-space: pre-wrap;
    word-break: break-all;
}

.bulk-actions-bar {
    display: flex;
    justify-content: flex-end; /* 将按钮推到右边 */
    margin-bottom: 10px;
}

#source-detail-table th:first-child,
#source-detail-table td:first-child {
    width: 1%;
    padding-left: 20px; /* Match table header padding */
}

.egid-detail-list {
    list-style: none;
    padding: 0;
}

.egid-detail-list .season-header {
    font-size: 1.2em;
    font-weight: bold;
    margin-top: 20px;
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid var(--border-color);
}

.egid-detail-list .episode-item {
    padding: 5px 0;
}
