# 服务器配置
server:
  host: "0.0.0.0"
  port: 7768

# 数据库配置 - 支持 TiDB 远程连接
database:
  host: "gateway01.ap-southeast-1.prod.aws.tidbcloud.com"  # TiDB 连接地址
  port: 4000  # TiDB 默认端口
  user: "2bmga6guXNxtZeu.root"  # 请修改为您的 TiDB 用户名
  password: "YgcazSj3khcq1D3G"  # 请修改为您的 TiDB 密码
  name: "danmu"  # 数据库名称

  # TLS/SSL 配置 - TiDB 要求启用 TLS
  ssl_disabled: false  # 不禁用SSL，TiDB需要TLS连接
  ssl_verify_cert: true  # 验证服务器证书
  ssl_verify_identity: true  # 验证服务器身份
  ssl_ca: "config/ca.pem"  # CA证书路径（TiDB Cloud通常不需要）
  ssl_cert: null  # 客户端证书路径（如果需要双向认证）
  ssl_key: null  # 客户端私钥路径（如果需要双向认证）

# JWT (JSON Web Token) 配置
jwt:
  secret_key: "your_subyoS74S86STOFTOesi56e743865ase_change_it" # 请务必修改为一个复杂且唯一的密钥
  algorithm: "HS256"
  access_token_expire_minutes: 1440 # 令牌有效期（分钟），例如 1天

# 初始管理员配置
admin:
  initial_user: "admin"  # 初始管理员用户名
  initial_password: "a19981216."  # 留空将自动生成随机密码

# 日志配置
log:
  level: "INFO"  # 日志级别: DEBUG, INFO, WARNING, ERROR
